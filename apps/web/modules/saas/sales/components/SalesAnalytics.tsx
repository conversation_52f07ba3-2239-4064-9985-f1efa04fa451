"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  DollarSignIcon,
  TrendingUpIcon,
  ShoppingCartIcon,
  FilterIcon,
  EyeIcon,
  UploadIcon
} from "lucide-react";

interface SalesAnalyticsProps {
  organizationId: string;
}

export function SalesAnalytics({ organizationId }: SalesAnalyticsProps) {
  const salesData = {
    revenue: 0,
    netRevenue: 0,
    totalSales: 0,
    monthlyGrowth: 0,
    averageTicket: 0
  };

  return (
    <div className="space-y-6">
      {/* Cartões de Métricas */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Faturamento</CardTitle>
            <DollarSignIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R$ {salesData.revenue.toLocaleString('pt-BR')}</div>
            <div className="w-full bg-blue-500 h-1 mt-2 rounded"></div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Receita líquida</CardTitle>
            <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R$ {salesData.netRevenue.toLocaleString('pt-BR')}</div>
            <div className="w-full bg-blue-500 h-1 mt-2 rounded"></div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de vendas</CardTitle>
            <ShoppingCartIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{salesData.totalSales}</div>
            <div className="w-full bg-blue-500 h-1 mt-2 rounded"></div>
          </CardContent>
        </Card>
      </div>

      {/* Seção de Vendas */}
      <Card>
        <CardHeader>
          <CardTitle>Vendas</CardTitle>
          <CardDescription>
            Gerencie e acompanhe todas as vendas da sua organização
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Barra de Ações */}
            <div className="flex items-center justify-between">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Buscar por CPF, ID da transação, e-mail ou nome"
                  className="pl-8 pr-3 py-2 border rounded-lg bg-background text-sm w-80"
                />
                <DollarSignIcon className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              </div>

              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <FilterIcon className="h-4 w-4" />
                </Button>

                <Button variant="outline" size="sm">
                  <EyeIcon className="h-4 w-4" />
                </Button>

                <Button variant="outline" size="sm">
                  <UploadIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Tabela de Vendas */}
            <div className="border rounded-lg">
              <div className="bg-muted/50 px-4 py-3 border-b">
                <div className="grid grid-cols-8 gap-4 text-sm font-medium">
                  <div className="flex items-center gap-1">
                    ID
                    <span className="text-muted-foreground">?</span>
                  </div>
                  <div>Produto(s)</div>
                  <div>Comprador</div>
                  <div>Vendedor</div>
                  <div>Data da venda</div>
                  <div>Método</div>
                  <div>Valor</div>
                  <div>Status</div>
                </div>
              </div>

              {/* Estado Vazio */}
              <div className="py-16 text-center">
                <div className="mx-auto w-24 h-24 bg-muted/50 rounded-full flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                    <span className="text-2xl">📄</span>
                  </div>
                </div>
                <h3 className="text-lg font-medium mb-2">Nenhuma venda registrada</h3>
                <p className="text-muted-foreground">
                  Divulgue seus produtos e acompanhe os resultados aqui.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
