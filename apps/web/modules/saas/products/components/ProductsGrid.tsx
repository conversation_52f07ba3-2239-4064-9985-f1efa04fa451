"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  PackageIcon,
  EditIcon,
  MoreHorizontalIcon,
  EyeIcon
} from "lucide-react";

interface ProductsGridProps {
  organizationId: string;
}

interface Product {
  id: string;
  name: string;
  type: string;
  status: "active" | "inactive" | "draft";
  category: "authorial" | "affiliation" | "coproduction";
  image: string;
  description: string;
  price: number;
  commission?: number;
}

// Dados mockados baseados na imagem
const mockProducts: Product[] = [
  {
    id: "1",
    name: "Plataforma Digital",
    type: "Serviço",
    status: "active",
    category: "authorial",
    image: "/images/products/platform-digital.jpg",
    description: "Plataforma completa para gestão de negócios digitais",
    price: 299.90
  },
  {
    id: "2",
    name: "CineFlick Card",
    type: "Serviço",
    status: "active",
    category: "affiliation",
    image: "/images/products/cineflick-card.jpg",
    description: "Cartão de desconto para cinemas com 60% de comissão",
    price: 99.90,
    commission: 60
  }
];

export function ProductsGrid({ organizationId }: ProductsGridProps) {
  const getStatusBadge = (status: Product["status"]) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 border border-green-200">ATIVO</Badge>;
      case "inactive":
        return <Badge className="bg-red-100 text-red-800 border border-red-200">INATIVO</Badge>;
      case "draft":
        return <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200">RASCUNHO</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200">DESCONHECIDO</Badge>;
    }
  };

  const getCategoryBadge = (category: Product["category"]) => {
    const colors = {
      "authorial": "bg-blue-100 text-blue-800 border border-blue-200",
      "affiliation": "bg-purple-100 text-purple-800 border border-purple-200",
      "coproduction": "bg-orange-100 text-orange-800 border border-orange-200"
    };

    const labels = {
      "authorial": "AUTORAL",
      "affiliation": "AFILIAÇÃO",
      "coproduction": "COPRODUÇÃO"
    };

    return (
      <Badge className={colors[category]}>
        {labels[category]}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Produtos</CardTitle>
        <CardDescription>
          Gerencie seus produtos e serviços
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {mockProducts.map((product) => (
            <Card key={product.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="aspect-square bg-muted rounded-lg mb-3 flex items-center justify-center">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <PackageIcon className="h-16 w-16 text-muted-foreground" />
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg line-clamp-2">{product.name}</CardTitle>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontalIcon className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{product.type}</Badge>
                    {getStatusBadge(product.status)}
                    {getCategoryBadge(product.category)}
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {product.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="text-lg font-bold">
                      R$ {product.price.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </div>

                    {product.commission && (
                      <Badge variant="secondary" className="bg-green-50 text-green-700">
                        {product.commission}% comissão
                      </Badge>
                    )}
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <EyeIcon className="h-4 w-4 mr-2" />
                      Visualizar
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <EditIcon className="h-4 w-4 mr-2" />
                      Editar
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Card para adicionar novo produto */}
          <Card className="hover:shadow-lg transition-shadow border-dashed">
            <CardContent className="flex flex-col items-center justify-center h-64">
              <PackageIcon className="h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Adicionar Produto</h3>
              <p className="text-sm text-muted-foreground text-center mb-4">
                Crie um novo produto ou serviço para sua organização
              </p>
              <Button>
                <PackageIcon className="h-4 w-4 mr-2" />
                Novo Produto
              </Button>
            </CardContent>
          </Card>
        </div>

        {mockProducts.length === 0 && (
          <div className="text-center py-16">
            <PackageIcon className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Nenhum produto encontrado</h3>
            <p className="text-muted-foreground mb-4">
              Comece criando seu primeiro produto
            </p>
            <Button>
              <PackageIcon className="h-4 w-4 mr-2" />
              Criar Produto
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
