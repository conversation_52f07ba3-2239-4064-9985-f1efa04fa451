import { OtpForm } from "@saas/auth/components/OtpForm";
import { getTranslations } from "next-intl/server";
import { Logo } from "@shared/components/Logo";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("auth.verify.title"),
	};
}

export default function VerifyPage() {
	return (
		<>
			<div className="flex flex-col gap-y-8 text-center">
				<div className="flex justify-center">
					<Logo size={60} />
				</div>
				<div className="flex flex-col gap-4">
					<h1 className="text-2xl font-bold text-foreground">
						Verificar conta
					</h1>
					<p className="text-lg text-muted-foreground">
						Digite o código de verificação do seu app autenticador
					</p>
				</div>
			</div>
			<OtpForm />
		</>
	);
}
