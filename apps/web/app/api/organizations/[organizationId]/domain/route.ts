import { NextRequest, NextResponse } from "next/server";
import { updateDomainConfig, enableCustomDomain, validateDomain } from "../../../../../lib/branding";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { z } from "zod";

const domainSchema = z.object({
  customDomain: z.string().optional(),
  sslEnabled: z.boolean().optional(),
  redirectWww: z.boolean().optional(),
});

export async function POST(request: NextRequest, { params }: { params: { organizationId: string } }) {
  try {
    const { db } = await import("@repo/database/prisma/client");
    const organization = await db.organization.findUnique({
      where: { id: params.organizationId }
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organização não encontrada" }, { status: 404 });
    }

    if (!organization.enableCustomDomain) {
      return NextResponse.json({ error: "Domínio personalizado requer plano Pro" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = domainSchema.parse(body);

    // Validate domain if provided
    if (validatedData.customDomain && !validateDomain(validatedData.customDomain)) {
      return NextResponse.json({ error: "Domínio inválido" }, { status: 400 });
    }

    // Check if domain is already in use
    if (validatedData.customDomain) {
      const { db } = await import("@repo/database/prisma/client");
      const existingOrg = await db.organization.findFirst({
        where: {
          customDomain: validatedData.customDomain,
          id: { not: organization.id }
        }
      });

      if (existingOrg) {
        return NextResponse.json({ error: "Domínio já está em uso" }, { status: 409 });
      }
    }

    await updateDomainConfig(organization.id, validatedData);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating domain config:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Dados inválidos", details: error.errors }, { status: 400 });
    }

    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
  }
}

export async function GET(request: NextRequest, { params }: { params: { organizationId: string } }) {
  try {
    const { db } = await import("@repo/database/prisma/client");
    const organization = await db.organization.findUnique({
      where: { id: params.organizationId }
    });
    
    if (!organization) {
      return NextResponse.json({ error: "Organização não encontrada" }, { status: 404 });
    }

    const { getDomainConfig } = await import("../../../../../lib/branding");
    const domainConfig = await getDomainConfig(organization.id);

    return NextResponse.json({ domainConfig });
  } catch (error) {
    console.error("Error fetching domain config:", error);
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
  }
}