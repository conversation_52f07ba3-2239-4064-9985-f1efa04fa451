import { NextRequest, NextResponse } from "next/server";
import { updateBrandingConfig, enableCustomBranding, validateColor } from "@/modules/branding/lib/branding";
import { z } from "zod";
import { getBrandingConfig } from "@/modules/branding/lib/branding";

const brandingSchema = z.object({
  primaryColor: z.string().optional(),
  secondaryColor: z.string().optional(),
  accentColor: z.string().optional(),
  backgroundColor: z.string().optional(),
  textColor: z.string().optional(),
  linkColor: z.string().optional(),
  buttonColor: z.string().optional(),
  borderColor: z.string().optional(),
  logoUrl: z.string().url().optional().or(z.literal("")),
  faviconUrl: z.string().url().optional().or(z.literal("")),
  companyName: z.string().optional(),
  tagline: z.string().optional(),
  description: z.string().optional(),
  darkMode: z.boolean().optional(),
  hideSystemBranding: z.boolean().optional(),
  customCss: z.string().optional(),
  fontFamily: z.string().optional(),
  borderRadius: z.string().optional(),
});

export async function POST(request: NextRequest, { params }: { params: { organizationId: string } }) {
  try {
    const { db } = await import("@repo/database/prisma/client");
    const organization = await db.organization.findUnique({
      where: { id: params.organizationId }
    });

    if (!organization) {
      return NextResponse.json({ error: "Organização não encontrada" }, { status: 404 });
    }

    const body = await request.json();
    const validatedData = brandingSchema.parse(body);

    // Validate colors if provided
    const colorFields = ['primaryColor', 'secondaryColor', 'accentColor', 'backgroundColor', 'textColor', 'linkColor', 'buttonColor', 'borderColor'];
    for (const field of colorFields) {
      const color = validatedData[field as keyof typeof validatedData] as string;
      if (color && !validateColor(color)) {
        return NextResponse.json({ error: `Cor inválida: ${field}` }, { status: 400 });
      }
    }

    // Check if organization has custom branding enabled for advanced features
    const advancedFeatures = ['tagline', 'description', 'hideSystemBranding', 'customCss'];
    const hasAdvancedFeatures = advancedFeatures.some(field => validatedData[field as keyof typeof validatedData]);

    if (hasAdvancedFeatures && !organization.enableCustomBranding) {
      return NextResponse.json({ error: "Recursos avançados requerem plano Pro" }, { status: 403 });
    }

    await updateBrandingConfig(organization.id, validatedData);

    // Enable custom branding if not already enabled and user is using branding features
    if (!organization.enableCustomBranding && Object.keys(validatedData).length > 0) {
      await enableCustomBranding(organization.id);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating branding config:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Dados inválidos", details: error.errors }, { status: 400 });
    }

    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
  }
}

export async function GET(request: NextRequest, { params }: { params: { organizationId: string } }) {
  try {
    const { db } = await import("@repo/database/prisma/client");
    const organization = await db.organization.findUnique({
      where: { id: params.organizationId }
    });

    if (!organization) {
      return NextResponse.json({ error: "Organização não encontrada" }, { status: 404 });
    }

    // const { getBrandingConfig } = await import("../../../../../lib/branding");
    const brandingConfig = await getBrandingConfig(organization.id);

    return NextResponse.json({ brandingConfig });
  } catch (error) {
    console.error("Error fetching branding config:", error);
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
  }
}
