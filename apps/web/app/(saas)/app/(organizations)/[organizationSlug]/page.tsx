import { Suspense } from "react";
import { notFound } from "next/navigation";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { MetricCard } from "@/modules/dashboard/components/MetricCard";
import { RevenueChart } from "@/modules/dashboard/components/RevenueChart";
import { MonthWidget } from "@/modules/dashboard/components/MonthWidget";
import { RevenueWidget } from "@/modules/dashboard/components/RevenueWidget";
import { CheckoutsWidget } from "@/modules/dashboard/components/CheckoutsWidget";
import { OrdersWidget } from "@/modules/dashboard/components/OrdersWidget";
import {
	CalendarIcon,
	DollarSignIcon,
	ShoppingCartIcon,
	PackageIcon,
	BarChart3Icon,
} from "lucide-react";
import { PageTransitions, CardTransitions, ListTransitions, SlideIn } from "@ui/components";

const mockChartData = [
	{ month: "Jan", revenue: 4000, orders: 240 },
	{ month: "Fev", revenue: 3000, orders: 139 },
	{ month: "Mar", revenue: 2000, orders: 980 },
	{ month: "Abr", revenue: 2780, orders: 390 },
	{ month: "Mai", revenue: 1890, orders: 480 },
	{ month: "Jun", revenue: 2390, orders: 380 },
];

const checkoutData = [
	{ label: "Concluídos", value: 1234, percentage: 78 },
	{ label: "Abandonados", value: 345, percentage: 22 },
];

const orderStatusData = [
	{ status: "Entregues", count: 156, color: "bg-green-500" },
	{ status: "Processando", count: 89, color: "bg-blue-500" },
	{ status: "Pendentes", count: 45, color: "bg-yellow-500" },
	{ status: "Cancelados", count: 12, color: "bg-red-500" },
];

export default async function OrganizationDashboard({
	params,
}: {
	params: { organizationSlug: string };
}) {
	const organization = await getActiveOrganization(params.organizationSlug);

	if (!organization) {
		return notFound();
	}

	return (
		<PageTransitions>
			<div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
				<SlideIn>
					<div className="flex items-center justify-between space-y-2">
						<h2 className="text-3xl font-bold tracking-tight">Painel de Controle</h2>
						<div className="flex items-center space-x-2">
							<CalendarIcon className="h-4 w-4" />
							<span className="text-sm text-muted-foreground">01 de Janeiro - 31 de Janeiro, 2024</span>
						</div>
					</div>
				</SlideIn>

				<CardTransitions>
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
						<MetricCard
							title="Receita Total"
							value="$45.231,89"
							description="+20,1% em relação ao mês passado"
							icon={DollarSignIcon}
							trend={{ value: "+20,1%", isPositive: true }}
						/>
						<MetricCard
							title="Pedidos"
							value="+2350"
							description="+180,1% em relação ao mês passado"
							icon={ShoppingCartIcon}
							trend={{ value: "+180,1%", isPositive: true }}
						/>
						<MetricCard
							title="Produtos"
							value="+12.234"
							description="+19% em relação ao mês passado"
							icon={PackageIcon}
							trend={{ value: "+19%", isPositive: true }}
						/>
						<MetricCard
							title="Ativos Agora"
							value="+573"
							description="+201 desde a última hora"
							icon={BarChart3Icon}
							trend={{ value: "+201", isPositive: true }}
						/>
					</div>

					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-4">
						<MonthWidget
							title="Este Mês"
							currentMonth={{ value: "$12.345", label: "Dezembro 2024" }}
							previousMonth={{ value: "$10.234", label: "Novembro 2024" }}
							trend={{ value: "+20,6%", isPositive: true }}
						/>
						<RevenueWidget
							title="Visão Geral da Receita"
							totalRevenue="$45.231,89"
							trend={{ value: "+12,5%", isPositive: true }}
							data={mockChartData.map(item => ({ month: item.month, value: item.revenue }))}
						/>
						<CheckoutsWidget
							title="Checkouts"
							totalCheckouts="1.579"
							conversionRate="3,2%"
							trend={{ value: "+8,1%", isPositive: true }}
							data={checkoutData}
						/>
						<OrdersWidget
							title="Status dos Pedidos"
							totalOrders="302"
							trend={{ value: "+15,3%", isPositive: true }}
							statusData={orderStatusData}
						/>
					</div>
				</CardTransitions>

				<ListTransitions>
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
						<Card className="col-span-4">
							<CardHeader>
								<CardTitle>Visão Geral da Receita</CardTitle>
							</CardHeader>
							<CardContent className="pl-2">
								<div className="h-[350px] w-full">
									<div className="flex h-full items-end justify-between space-x-2 p-4">
										{mockChartData.map((data, index) => (
											<div key={index} className="flex flex-col items-center space-y-2">
												<div
													className="bg-primary rounded-sm transition-all hover:opacity-80"
													style={{
														height: `${(data.revenue / 4000) * 250}px`,
														width: "40px",
													}}
												/>
												<span className="text-xs text-muted-foreground">{data.month}</span>
											</div>
										))}
									</div>
								</div>
							</CardContent>
						</Card>
						<Card className="col-span-3">
							<CardHeader>
								<CardTitle>Vendas Recentes</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-8">
									<div className="flex items-center">
										<div className="ml-4 space-y-1">
											<p className="text-sm font-medium leading-none">Olivia Martin</p>
											<p className="text-sm text-muted-foreground"><EMAIL></p>
										</div>
										<div className="ml-auto font-medium">+$1.999,00</div>
									</div>
									<div className="flex items-center">
										<div className="ml-4 space-y-1">
											<p className="text-sm font-medium leading-none">Jackson Lee</p>
											<p className="text-sm text-muted-foreground"><EMAIL></p>
										</div>
										<div className="ml-auto font-medium">+$39,00</div>
									</div>
									<div className="flex items-center">
										<div className="ml-4 space-y-1">
											<p className="text-sm font-medium leading-none">Isabella Nguyen</p>
											<p className="text-sm text-muted-foreground"><EMAIL></p>
										</div>
										<div className="ml-auto font-medium">+$299,00</div>
									</div>
									<div className="flex items-center">
										<div className="ml-4 space-y-1">
											<p className="text-sm font-medium leading-none">William Kim</p>
											<p className="text-sm text-muted-foreground"><EMAIL></p>
										</div>
										<div className="ml-auto font-medium">+$99,00</div>
									</div>
									<div className="flex items-center">
										<div className="ml-4 space-y-1">
											<p className="text-sm font-medium leading-none">Sofia Davis</p>
											<p className="text-sm text-muted-foreground"><EMAIL></p>
										</div>
										<div className="ml-auto font-medium">+$39,00</div>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>
				</ListTransitions>
			</div>
		</PageTransitions>
	);
}
