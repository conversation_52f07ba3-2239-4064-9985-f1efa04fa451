import { getActiveOrganization } from "@saas/auth/lib/server";
import { ProductsGrid } from "@saas/products/components/ProductsGrid";
import { ProductFilters } from "@saas/products/components/ProductFilters";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Button } from "@ui/components/button";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";

export default async function ProductsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Produtos"
        subtitle="Gerencie seus produtos e serviços"
        action={
          <Link href={`/app/${organizationSlug}/products/new`}>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Novo Produto
            </Button>
          </Link>
        }
      />

      <ProductFilters organizationId={organization.id} />
      <ProductsGrid organizationId={organization.id} />
    </div>
  );
}
